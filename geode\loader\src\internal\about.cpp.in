#include "about.hpp"

geode::VersionInfo about::getLoaderVersion() {
    return {
        @PROJECT_VERSION_MAJOR@,
        @PROJECT_VERSION_MINOR@,
        @PROJECT_VERSION_PATCH@,
        @PROJECT_VERSION_TAG_CONSTR@
    };
}
const char* about::getLoaderVersionStr() { return "@PROJECT_VERSION@@PROJECT_VERSION_SUFFIX@"; }
const char* about::getLoaderCommitHash() { return "@GEODE_COMMIT_HASH@"; }
const char* about::getBindingsCommitHash() { return "@GEODE_BINDINGS_COMMIT_HASH@"; }
const char* about::getLoaderModJson() { return R"JSON_SEPARATOR(@LOADER_MOD_JSON@)JSON_SEPARATOR"; }
