name: Build Geode Mod

on:
  workflow_dispatch:
  push:
    branches:
      - "**"

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        config:
        - name: Windows
          os: windows-latest

        - name: macOS
          os: macos-latest

        - name: iOS
          os: macos-latest
          target: iOS

        - name: Android32
          os: ubuntu-latest
          target: Android32

        - name: Android64
          os: ubuntu-latest
          target: Android64

    name: ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}

    steps:
      - uses: actions/checkout@v4

      - name: Build the mod
        uses: geode-sdk/build-geode-mod@main
        with:
          combine: true
          target: ${{ matrix.config.target }}
          # sdk: nightly

  package:
    name: Package builds
    runs-on: ubuntu-latest
    needs: ['build']

    steps:
      - uses: geode-sdk/build-geode-mod/combine@main
        id: build

      - uses: actions/upload-artifact@v4
        with:
          name: Build Output
          path: ${{ steps.build.outputs.build-output }}
