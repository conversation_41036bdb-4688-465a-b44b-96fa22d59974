{"geode": "@PROJECT_VERSION@@PROJECT_VERSION_SUFFIX@", "gd": {"win": "*", "mac": "*", "android": "*"}, "id": "geode.loader", "version": "@PROJECT_VERSION@@PROJECT_VERSION_SUFFIX@", "name": "Geode", "developer": "Geode Team", "description": "The Geode mod loader", "links": {"community": "https://discord.com/invite/9e43WMKzhp", "homepage": "https://geode-sdk.org", "source": "https://github.com/geode-sdk/geode"}, "repository": "https://github.com/geode-sdk/geode", "resources": {"fonts": {"mdFont": {"path": "fonts/Ubuntu-Regular.ttf", "size": 80}, "mdFontB": {"path": "fonts/Ubuntu-Bold.ttf", "size": 80}, "mdFontI": {"path": "fonts/Ubuntu-Italic.ttf", "size": 80}, "mdFontBI": {"path": "fonts/Ubuntu-BoldItalic.ttf", "size": 80}, "mdFontMono": {"path": "fonts/UbuntuMono-Regular.ttf", "size": 80}}, "sprites": ["images/*.png", "swelve/*.png"], "files": ["sounds/*.ogg", "about.md", "changelog.md", "support.md", "mod.json", "version"], "spritesheets": {"LogoSheet": ["logos/*.png"], "APISheet": ["*.png"], "BlankSheet": ["blanks/*.png"], "EventSheet": ["modtober/*.png"]}}, "settings": {"auto-check-updates": {"type": "bool", "default": true, "name": "Check For Updates", "description": "Automatically check for <cy>Geode</c> updates on startup"}, "disable-last-crashed-popup": {"type": "bool", "default": false, "name": "Disable Crash Popup", "description": "Disables the popup at startup asking if you'd like to send a bug report; intended for developers"}, "enable-geode-theme": {"type": "bool", "default": true, "name": "Enable Geode-Themed Colors", "description": "When enabled, the Geode menu has a <ca>Geode-themed color scheme</c>. <cy>This does not affect any other menus!</c>"}, "infinite-local-mods-list": {"type": "bool", "default": false, "name": "Expand Installed Mods List", "description": "Make the installed mods list a single infinite scrollable list instead of having pages"}, "copy-mods": {"type": "custom:copy-mods", "name": ""}, "developer-title": {"type": "title", "name": "Developer Settings"}, "show-platform-console": {"type": "bool", "default": false, "name": "Show Platform Console", "description": "Show the native console (if one exists). <cr>This setting is meant for developers</c>", "platforms": ["win", "mac"], "requires-restart": true}, "console-log-level": {"type": "string", "default": "info", "name": "Console Log Level", "description": "Sets the log level for the <cb>platform console</c>.", "one-of": ["debug", "info", "warn", "error"]}, "file-log-level": {"type": "string", "default": "info", "name": "File Log Level", "description": "Sets the log level for the <cb>log files</c>.", "one-of": ["debug", "info", "warn", "error"]}, "server-cache-size-limit": {"type": "int", "default": 20, "min": 1, "max": 100, "name": "Server <PERSON><PERSON>", "description": "Limits the size of the cache used for loading mods. Higher values result in higher memory usage."}, "log-retention-period": {"type": "int", "default": 30, "min": 0, "max": 365, "name": "Log Retention Period", "description": "The number of days to keep logs for. Logs older than this will be deleted every launch. 0 to disable deletion."}}, "issues": {"info": "Post your issues on the <cp>Geode Github Repository</c>. <cy>Please follow the standard issue format</c>.", "url": "https://github.com/geode-sdk/geode/issues/new"}}