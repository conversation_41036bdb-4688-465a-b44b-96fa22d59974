/****************************************************************************
Copyright (c) 2010-2012 cocos2d-x.org
Copyright (c) 2008-2010 Ricardo <PERSON>
Copyright (c) 2011      Zynga Inc.

http://www.cocos2d-x.org

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
****************************************************************************/
#ifndef __CCPARTICLE_EXAMPLE_H__
#define __CCPARTICLE_EXAMPLE_H__

#include "CCParticleSystemQuad.h"

NS_CC_BEGIN

/**
 * @addtogroup particle_nodes
 * @{
 */

//! @brief A fire particle system
class CC_DLL CCParticleFire : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleFire(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleFire(){}
    bool init(){ return initWithTotalParticles(250); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleFire* create();
    static CCParticleFire* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief A fireworks particle system
class CC_DLL CCParticleFireworks : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleFireworks(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleFireworks(){}
    bool init(){ return initWithTotalParticles(1500); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleFireworks* create();
    static CCParticleFireworks* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief A sun particle system
class CC_DLL CCParticleSun : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleSun(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleSun(){}
    bool init(){ return initWithTotalParticles(350); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleSun* create();
    static CCParticleSun* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief A galaxy particle system
class CC_DLL CCParticleGalaxy : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleGalaxy(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleGalaxy(){}
    bool init(){ return initWithTotalParticles(200); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleGalaxy* create();
    static CCParticleGalaxy* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief A flower particle system
class CC_DLL CCParticleFlower : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleFlower(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleFlower(){}
    bool init(){ return initWithTotalParticles(250); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleFlower* create();
    static CCParticleFlower* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief A meteor particle system
class CC_DLL CCParticleMeteor : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleMeteor(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleMeteor(){}
    bool init(){ return initWithTotalParticles(150); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);

    static CCParticleMeteor * create();
    static CCParticleMeteor* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief An spiral particle system
class CC_DLL CCParticleSpiral : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleSpiral(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleSpiral(){}
    bool init(){ return initWithTotalParticles(500); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleSpiral* create();
    static CCParticleSpiral* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief An explosion particle system
class CC_DLL CCParticleExplosion : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleExplosion(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleExplosion(){}
    bool init(){ return initWithTotalParticles(700); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleExplosion* create();
    static CCParticleExplosion* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief An smoke particle system
class CC_DLL CCParticleSmoke : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleSmoke(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleSmoke(){}
    bool init(){ return initWithTotalParticles(200); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleSmoke* create();
    static CCParticleSmoke* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief An snow particle system
class CC_DLL CCParticleSnow : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleSnow(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleSnow(){}
    bool init(){ return initWithTotalParticles(700); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleSnow* create();
    static CCParticleSnow* createWithTotalParticles(unsigned int numberOfParticles);
};

//! @brief A rain particle system
class CC_DLL CCParticleRain : public CCParticleSystemQuad
{
    GEODE_FRIEND_MODIFY
public:
    /**
     * @js ctor
     */
    CCParticleRain(){}
    /**
     * @js NA
     * @lua NA
     */
    virtual ~CCParticleRain(){}
    bool init(){ return initWithTotalParticles(1000); }
    virtual bool initWithTotalParticles(unsigned int numberOfParticles);
    
    static CCParticleRain* create();
    static CCParticleRain* createWithTotalParticles(unsigned int numberOfParticles);
};

// end of particle_nodes group
/// @}

NS_CC_END

#endif //__CCPARTICLE_EXAMPLE_H__
