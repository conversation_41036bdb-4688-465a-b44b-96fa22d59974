/****************************************************************************
 Copyright (c) 2010 cocos2d-x.org

 http://www.cocos2d-x.org

 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.
 ****************************************************************************/
#ifndef __CC_PLATFORM_MACROS_H__
#define __CC_PLATFORM_MACROS_H__

/**
 * define some platform specific macros
 */
#include "../include/ccConfig.h"
#include "CCPlatformConfig.h"
#if (CC_TARGET_PLATFORM == CC_PLATFORM_WIN32)
    #include "win32/CCPlatformDefine.h"
#elif (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
    #include "ios/CCPlatformDefine.h"
#elif (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
    #include "android/CCPlatformDefine.h"
#elif (CC_TARGET_PLATFORM == CC_PLATFORM_MAC)
    #include "mac/CCPlatformDefine.h"
#endif

/**
 * define a create function for a specific type, such as CCLayer
 * @__TYPE__ class type to add create(), such as CCLayer
 */
#define CREATE_FUNC(__TYPE__)            \
    static __TYPE__* create() {          \
        __TYPE__* pRet = new __TYPE__(); \
        if (pRet && pRet->init()) {      \
            pRet->autorelease();         \
            return pRet;                 \
        }                                \
        else {                           \
            delete pRet;                 \
            pRet = NULL;                 \
            return NULL;                 \
        }                                \
    }

/**
 * define a node function for a specific type, such as CCLayer
 * @__TYPE__ class type to add node(), such as CCLayer
 * @deprecated: This interface will be deprecated sooner or later.
 */
#define NODE_FUNC(__TYPE__)                           \
    CC_DEPRECATED_ATTRIBUTE static __TYPE__* node() { \
        __TYPE__* pRet = new __TYPE__();              \
        if (pRet && pRet->init()) {                   \
            pRet->autorelease();                      \
            return pRet;                              \
        }                                             \
        else {                                        \
            delete pRet;                              \
            pRet = NULL;                              \
            return NULL;                              \
        }                                             \
    }

/** @def CC_ENABLE_CACHE_TEXTURE_DATA
Enable it if you want to cache the texture data.
Basically, it's only enabled for Emscripten.

It's new in cocos2d-x since v0.99.5
*/
#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID) || \
    (CC_TARGET_PLATFORM == CC_PLATFORM_EMSCRIPTEN) || (CC_TARGET_PLATFORM == CC_PLATFORM_WP8)
    #define CC_ENABLE_CACHE_TEXTURE_DATA 1
#else
    #define CC_ENABLE_CACHE_TEXTURE_DATA 0
#endif

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID) || (CC_TARGET_PLATFORM == CC_PLATFORM_WIN32) || \
    (CC_TARGET_PLATFORM == CC_PLATFORM_EMSCRIPTEN)
    /* Application will crash in glDrawElements function on some win32 computers and some android
       devices. Indices should be bound again while drawing to avoid this bug.
     */
    #define CC_REBIND_INDICES_BUFFER 1
#else
    #define CC_REBIND_INDICES_BUFFER 0
#endif

// generic macros

// namespace cocos2d {}
/**
 * Geode specific:
 * Since Geode uses gd namespace for stl support, this needs to be reflected for
 * classes using stl containers. Easiest way to hijack this is redefining the NS_CC_BEGIN
 * macro.
 */
class GeodeNodeMetadata;

#include <stdint.h>
#include <string>
#include <type_traits>
#include <variant>

namespace tulip::hook {
    struct HandlerMetadata;
}

namespace geode {
    template <class, class>
    class Result;

    namespace modifier {
        class FieldContainer;

        template <class Derived, class Base>
        class ModifyDerive;

        template <uint32_t>
        uintptr_t address();

        Result<tulip::hook::HandlerMetadata, std::string> handlerMetadataForAddress(uintptr_t address);
    }

    namespace addresser {
        template <class Class>
        Class* friendCreate(typename std::void_t<decltype(static_cast<Class* (*)()>(&Class::create))>*);
    }
}

#define GEODE_FRIEND_MODIFY                                         \
    template <class Derived, class Base>                            \
    friend class ::geode::modifier::ModifyDerive;                   \
    friend class ::GeodeNodeMetadata;                               \
    template <uint32_t>                                             \
    friend uintptr_t geode::modifier::address();                    \
    friend geode::Result<tulip::hook::HandlerMetadata, std::string> \
    geode::modifier::handlerMetadataForAddress(uintptr_t address);  \
    template <class Class>                                          \
    friend Class* geode::addresser::                                \
        friendCreate(typename std::void_t<decltype(static_cast<Class* (*)()>(&Class::create))>*);

#ifdef __cplusplus
    #define NS_CC_BEGIN namespace cocos2d {
    #define NS_CC_END }
    #define USING_NS_CC using namespace cocos2d
#else
    #define NS_CC_BEGIN
    #define NS_CC_END
    #define USING_NS_CC
#endif

/** CC_PROPERTY_READONLY is used to declare a protected variable.
 We can use getter to read the variable.
 @param varType : the type of variable.
 @param varName : variable name.
 @param funName : "get + funName" is the name of the getter.
 @warning : The getter is a public virtual function, you should rewrite
 it first. The variables and methods declared after CC_PROPERTY_READONLY
 are all public. If you need protected or private, please declare.
 */
#define CC_PROPERTY_READONLY(varType, varName, funName) \
                                                        \
protected:                                              \
    varType varName;                                    \
                                                        \
public:                                                 \
    virtual varType get##funName(void);

#define CC_PROPERTY_READONLY_PASS_BY_REF(varType, varName, funName) \
                                                                    \
protected:                                                          \
    varType varName;                                                \
                                                                    \
public:                                                             \
    virtual const varType& get##funName(void);

/** CC_PROPERTY is used to declare a protected variable.
 We can use getter to read the variable, and use the setter to change
 the variable.
 @param varType : the type of variable.
 @param varName : variable name.
 @param funName : "get + funName" is the name of the getter.
 "set + funName" is the name of the setter.
 @warning : The getter and setter are public virtual functions, you
 should rewrite them first. The variables and methods declared after
 CC_PROPERTY are all public. If you need protected or private, please
 declare.
 */
#define CC_PROPERTY(varType, varName, funName) \
                                               \
protected:                                     \
    varType varName;                           \
                                               \
public:                                        \
    virtual varType get##funName(void);        \
                                               \
public:                                        \
    virtual void set##funName(varType var);

#define CC_PROPERTY_PASS_BY_REF(varType, varName, funName) \
                                                           \
protected:                                                 \
    varType varName;                                       \
                                                           \
public:                                                    \
    virtual const varType& get##funName(void);             \
                                                           \
public:                                                    \
    virtual void set##funName(const varType& var);

/** CC_SYNTHESIZE_READONLY is used to declare a protected variable.
 We can use getter to read the variable.
 @param varType : the type of variable.
 @param varName : variable name.
 @param funName : "get + funName" is the name of the getter.
 @warning : The getter is a public inline function.
 The variables and methods declared after CC_SYNTHESIZE_READONLY are all
 public. If you need protected or private, please declare.
 */
#define CC_SYNTHESIZE_READONLY(varType, varName, funName) \
                                                          \
protected:                                                \
    varType varName;                                      \
                                                          \
public:                                                   \
    virtual varType get##funName(void) const { return varName; }

#define CC_SYNTHESIZE_READONLY_NC(varType, varName, funName) \
                                                             \
protected:                                                   \
    varType varName;                                         \
                                                             \
public:                                                      \
    virtual varType get##funName(void) { return varName; }

#define CC_SYNTHESIZE_READONLY_PASS_BY_REF(varType, varName, funName) \
                                                                      \
protected:                                                            \
    varType varName;                                                  \
                                                                      \
public:                                                               \
    virtual const varType& get##funName(void) const { return varName; }

/** CC_SYNTHESIZE is used to declare a protected variable.
 We can use getter to read the variable, and use the setter to change the variable.
 @param varType : the type of variable.
 @param varName : variable name.
 @param funName : "get + funName" is the name of the getter.
 "set + funName" is the name of the setter.
 @warning : The getter and setter are public  inline functions.
 The variables and methods declared after CC_SYNTHESIZE are all public.
 If you need protected or private, please declare.
 */
#define CC_SYNTHESIZE(varType, varName, funName)                 \
                                                                 \
protected:                                                       \
    varType varName;                                             \
                                                                 \
public:                                                          \
    virtual varType get##funName(void) const { return varName; } \
                                                                 \
public:                                                          \
    virtual void set##funName(varType var) { varName = var; }

#define CC_SYNTHESIZE_PASS_BY_REF(varType, varName, funName)            \
                                                                        \
protected:                                                              \
    varType varName;                                                    \
                                                                        \
public:                                                                 \
    virtual const varType& get##funName(void) const { return varName; } \
                                                                        \
public:                                                                 \
    virtual void set##funName(const varType& var) { varName = var; }

#define CC_SYNTHESIZE_RETAIN(varType, varName, funName)          \
                                                                 \
private:                                                         \
    varType varName;                                             \
                                                                 \
public:                                                          \
    virtual varType get##funName(void) const { return varName; } \
                                                                 \
public:                                                          \
    virtual void set##funName(varType var) {                     \
        if (varName != var) {                                    \
            CC_SAFE_RETAIN(var);                                 \
            CC_SAFE_RELEASE(varName);                            \
            varName = var;                                       \
        }                                                        \
    }

#define CC_SAFE_DELETE(p) \
    do {                  \
        if (p) {          \
            delete (p);   \
            (p) = 0;      \
        }                 \
    } while (0)
#define CC_SAFE_DELETE_ARRAY(p) \
    do {                        \
        if (p) {                \
            delete[] (p);       \
            (p) = 0;            \
        }                       \
    } while (0)
#define CC_SAFE_FREE(p) \
    do {                \
        if (p) {        \
            free(p);    \
            (p) = 0;    \
        }               \
    } while (0)
#define CC_SAFE_RELEASE(p)  \
    do {                    \
        if (p) {            \
            (p)->release(); \
        }                   \
    } while (0)
#define CC_SAFE_RELEASE_NULL(p) \
    do {                        \
        if (p) {                \
            (p)->release();     \
            (p) = 0;            \
        }                       \
    } while (0)
#define CC_SAFE_RETAIN(p)  \
    do {                   \
        if (p) {           \
            (p)->retain(); \
        }                  \
    } while (0)
#define CC_BREAK_IF(cond) \
    if (cond) break

#define __CCLOGWITHFUNCTION(s, ...) \
    CCLog("%s : %s", __FUNCTION__, CCString::createWithFormat(s, ##__VA_ARGS__)->getCString())

// cocos2d debug
#if !defined(COCOS2D_DEBUG) || COCOS2D_DEBUG == 0
    #define CCLOG(...) \
        do {           \
        } while (0)
    #define CCLOGINFO(...) \
        do {               \
        } while (0)
    #define CCLOGERROR(...) \
        do {                \
        } while (0)
    #define CCLOGWARN(...) \
        do {               \
        } while (0)

#elif COCOS2D_DEBUG == 1
    #define CCLOG(format, ...) cocos2d::CCLog(format, ##__VA_ARGS__)
    #define CCLOGERROR(format, ...) cocos2d::CCLog(format, ##__VA_ARGS__)
    #define CCLOGINFO(format, ...) \
        do {                       \
        } while (0)
    #define CCLOGWARN(...) __CCLOGWITHFUNCTION(__VA_ARGS__)

#elif COCOS2D_DEBUG > 1
    #define CCLOG(format, ...) cocos2d::CCLog(format, ##__VA_ARGS__)
    #define CCLOGERROR(format, ...) cocos2d::CCLog(format, ##__VA_ARGS__)
    #define CCLOGINFO(format, ...) cocos2d::CCLog(format, ##__VA_ARGS__)
    #define CCLOGWARN(...) __CCLOGWITHFUNCTION(__VA_ARGS__)
#endif // COCOS2D_DEBUG

// Lua engine debug
#if !defined(COCOS2D_DEBUG) || COCOS2D_DEBUG == 0 || CC_LUA_ENGINE_DEBUG == 0
    #define LUALOG(...)
#else
    #define LUALOG(format, ...) cocos2d::CCLog(format, ##__VA_ARGS__)
#endif // Lua engine debug

#if defined(__GNUC__) && ((__GNUC__ >= 5) || ((__GNUG__ == 4) && (__GNUC_MINOR__ >= 4))) || \
    (defined(__clang__) && (__clang_major__ >= 3))
    #define CC_DISABLE_COPY(Class)    \
                                      \
    private:                          \
        Class(const Class&) = delete; \
        Class& operator=(const Class&) = delete;
#else
    #define CC_DISABLE_COPY(Class) \
                                   \
    private:                       \
        Class(const Class&);       \
        Class& operator=(const Class&);
#endif

/*
 * only certain compilers support __attribute__((deprecated))
 */
#if defined(__GNUC__) && ((__GNUC__ >= 4) || ((__GNUC__ == 3) && (__GNUC_MINOR__ >= 1)))
    #define CC_DEPRECATED_ATTRIBUTE __attribute__((deprecated))
#elif _MSC_VER >= 1400 // vs 2005 or higher
    #define CC_DEPRECATED_ATTRIBUTE __declspec(deprecated)
#else
    #define CC_DEPRECATED_ATTRIBUTE
#endif

/*
 * only certain compiler support __attribute__((format))
 * formatPos - 1-based position of format string argument
 * argPos - 1-based position of first format-dependent argument
 */
#if defined(__GNUC__) && (__GNUC__ >= 4)
    #define CC_FORMAT_PRINTF(formatPos, argPos) \
        __attribute__((__format__(printf, formatPos, argPos)))
    /** CC_FORMAT_PRINTF
     * Visual Studio 2019 has __has_attribute,
     * but __has_attribute(format) is undefined,
     * leaving CC_FORMAT_PRINTF undefined by default.
     */
#elif defined(__has_attribute) && !defined(_MSC_VER)
    #if __has_attribute(format)
        #define CC_FORMAT_PRINTF(formatPos, argPos) \
            __attribute__((__format__(printf, formatPos, argPos)))
    #endif
#else
    #define CC_FORMAT_PRINTF(formatPos, argPos)
#endif

#if defined(_MSC_VER)
    #define CC_FORMAT_PRINTF_SIZE_T "%08lX"
#else
    #define CC_FORMAT_PRINTF_SIZE_T "%08zX"
#endif

#ifdef __GNUC__
    #define CC_UNUSED __attribute__((unused))
#else
    #define CC_UNUSED
#endif

/** CC_PROPERTY_NV
 * CC_PROPERTY, but getters and setters are not virtual functions.
 */
#define CC_SYNTHESIZE_NV(varType, varName, funName)      \
                                                         \
protected:                                               \
    varType varName;                                     \
                                                         \
public:                                                  \
    varType get##funName(void) const { return varName; } \
                                                         \
public:                                                  \
    void set##funName(varType var) { varName = var; }

/**
 */
#define CC_SYNTHESIZE_READONLY_NV(varType, varName, funName) \
                                                             \
protected:                                                   \
    varType varName;                                         \
                                                             \
public:                                                      \
    varType get##funName(void) const { return varName; }

#define CC_SYNTHESIZE_READONLY_NV_NC(varType, varName, funName) \
                                                                \
protected:                                                      \
    varType varName;                                            \
                                                                \
public:                                                         \
    varType get##funName(void) { return varName; }

#endif // __CC_PLATFORM_MACROS_H__
