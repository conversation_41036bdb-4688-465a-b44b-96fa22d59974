### Generated by Winemaker 0.8.4
###
### Invocation command line was
### /usr/bin/winemaker -iws2_32 -ievdev . --single-target linux-input


SRCDIR                = .
SUBDIRS               =
DLLS                  =
LIBS                  =
EXES                  = linux-input



### Common settings

CEXTRA                =
CXXEXTRA              =
RCEXTRA               =
DEFINES               =
INCLUDE_PATH          =
DLL_PATH              =
DLL_IMPORTS           = ws2_32 \
			evdev
LIBRARY_PATH          =
LIBRARIES             =


### linux-input sources and settings

linux_input_MODULE    = linux-input
linux_input_C_SRCS    =
linux_input_CXX_SRCS  = linux-input.cpp
linux_input_RC_SRCS   =
linux_input_LDFLAGS   =
linux_input_ARFLAGS   =
linux_input_DLL_PATH  =
linux_input_DLLS      =
linux_input_LIBRARY_PATH=
linux_input_LIBRARIES =

linux_input_OBJS      = $(linux_input_C_SRCS:.c=.o) \
			$(linux_input_CXX_SRCS:.cpp=.o) \
			$(linux_input_RC_SRCS:.rc=.res)



### Global source lists

C_SRCS                = $(linux_input_C_SRCS)
CXX_SRCS              = $(linux_input_CXX_SRCS)
RC_SRCS               = $(linux_input_RC_SRCS)


### Tools

CC = winegcc
CXX = wineg++
RC = wrc
AR = ar


### Generic targets

all: $(SUBDIRS) $(DLLS:%=%.so) $(LIBS) $(EXES)

### Build rules

.PHONY: all clean dummy

$(SUBDIRS): dummy
	@cd $@ && $(MAKE)

# Implicit rules

.SUFFIXES: .cpp .cxx .rc .res
DEFINCL = $(INCLUDE_PATH) $(DEFINES) $(OPTIONS)

.c.o:
	$(CC) -c $(CFLAGS) $(CEXTRA) $(DEFINCL) -o $@ $<

.cpp.o:
	$(CXX) -c $(CXXFLAGS) $(CXXEXTRA) $(DEFINCL) -o $@ $<

.cxx.o:
	$(CXX) -c $(CXXFLAGS) $(CXXEXTRA) $(DEFINCL) -o $@ $<

.rc.res:
	$(RC) $(RCFLAGS) $(RCEXTRA) $(DEFINCL) -fo$@ $<

# Rules for cleaning

CLEAN_FILES     = y.tab.c y.tab.h lex.yy.c core *.orig *.rej \
                  \\\#*\\\# *~ *% .\\\#*

clean:: $(SUBDIRS:%=%/__clean__) $(EXTRASUBDIRS:%=%/__clean__)
	$(RM) $(CLEAN_FILES) $(RC_SRCS:.rc=.res) $(C_SRCS:.c=.o) $(CXX_SRCS:.cpp=.o)
	$(RM) $(DLLS:%=%.so) $(LIBS) $(EXES) $(EXES:%=%.so)

$(SUBDIRS:%=%/__clean__): dummy
	cd `dirname $@` && $(MAKE) clean

$(EXTRASUBDIRS:%=%/__clean__): dummy
	-cd `dirname $@` && $(RM) $(CLEAN_FILES)

### Target specific build rules
DEFLIB = $(LIBRARY_PATH) $(LIBRARIES) $(DLL_PATH) $(DLL_IMPORTS:%=-l%)

$(linux_input_MODULE): $(linux_input_OBJS)
	$(CXX) $(linux_input_LDFLAGS) -o $@ $(linux_input_OBJS) $(linux_input_LIBRARY_PATH) $(linux_input_DLL_PATH) $(DEFLIB) $(linux_input_DLLS:%=-l%) $(linux_input_LIBRARIES:%=-l%)


