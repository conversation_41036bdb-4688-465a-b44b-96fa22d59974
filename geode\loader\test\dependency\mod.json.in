{"geode": "@GEODE_VERSION_FULL@", "gd": {"win": "*", "mac": "*", "android": "*", "ios": "*"}, "version": "1.0.0", "id": "geode.testdep", "name": "Geode Test Dependency", "developer": "Geode Team", "description": "Unit test dependency for Geode", "api": {"include": []}, "settings": {"its-raining-after-all": {"type": "bool", "name": "It's Raining After All", "description": "dakedo yappari ame wa furun da ne", "default": true}, "trapped-in-the-past": {"type": "string", "default": "Ima ima ima ima", "match": "([iI]ma\\s?)*"}, "compared-child": {"type": "int", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": 2, "min": -20, "max": 20, "control": {"arrows": true, "arrow-step": 2, "big-arrows": true, "big-arrow-step": 5, "slider-step": 2, "input": false}}, "even-tears-withered": {"type": "string", "default": ";-;"}, "being-low-as-dirt-taking-whats-important-from-me": {"type": "float", "default": 5.5, "control": {"slider-step": 0.5}}, "loneliness-and-the-future": {"type": "string", "default": "hit<PERSON>bo<PERSON> ni", "one-of": ["hit<PERSON>bo<PERSON> ni", "mirai toka", "arun da<PERSON> ka"]}, "territory-battle": {"type": "color", "default": "#ff006f"}, "faithful-dog-hachi": {"type": "rgba", "default": [55, 224, 255, 255]}, "im-getting-to-the-bus-to-the-other-world-see-ya": {"type": "file", "name": "Bus", "default": "", "control": {"filters": [{"description": "Level Files", "files": ["*.gmd2", "*.gmd", "*.lvl"]}, {"description": "GMD Files", "files": ["*.gmd"]}]}}, "overcast-skies": {"type": "custom"}}}